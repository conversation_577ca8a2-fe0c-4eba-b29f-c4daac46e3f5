# ===========================================
# 停车场管理系统 - 环境配置文件模板
# ===========================================
# 复制此文件为 .env 并填入实际的配置值

# ===========================================
# 数据库配置
# ===========================================
MYSQL_ROOT_PASSWORD=your_mysql_root_password
MYSQL_DATABASE=parknew
MYSQL_USER=your_mysql_user
MYSQL_PASSWORD=your_mysql_password
MYSQL_HOST=park-mysql
MYSQL_PORT=3306

# Nacos数据库配置
NACOS_DB_NAME=nacos
NACOS_DB_USER=root
NACOS_DB_PASSWORD=your_nacos_db_password

# ===========================================
# Redis配置
# ===========================================
REDIS_PASSWORD=your_redis_password
REDIS_HOST=park-redis
REDIS_PORT=6379
REDIS_DATABASE_DEFAULT=0
REDIS_DATABASE_GATE=2

# ===========================================
# Nacos配置
# ===========================================
NACOS_SERVER_ADDR=park-nacos:8848
NACOS_USERNAME=nacos
NACOS_PASSWORD=your_nacos_password
NACOS_NAMESPACE=your_nacos_namespace

# Nacos认证配置
NACOS_AUTH_ENABLE=true
NACOS_AUTH_TOKEN=your_nacos_auth_token
NACOS_AUTH_IDENTITY_KEY=serverIdentity
NACOS_AUTH_IDENTITY_VALUE=security

# ===========================================
# 应用配置
# ===========================================
SPRING_PROFILES_ACTIVE=test

# ===========================================
# 服务端口配置
# ===========================================
MYSQL_EXTERNAL_PORT=3306
REDIS_EXTERNAL_PORT=6380
NACOS_HTTP_PORT=8848
NACOS_GRPC_PORT=9848
AUTH_SERVICE_PORT=9204
WX_AUTH_SERVICE_PORT=9205
SYSTEM_SERVICE_PORT=9201
FILE_SERVICE_PORT=9202
WX_SERVICE_PORT=9206
GATE_SERVICE_PORT=9203
GATEWAY_SERVICE_PORT=8080

# ===========================================
# 时区配置
# ===========================================
TZ=Asia/Shanghai

# ===========================================
# 资源限制配置
# ===========================================
# MySQL资源限制
MYSQL_MEMORY_LIMIT=1g
MYSQL_MEMORY_RESERVATION=512m
MYSQL_CPU_LIMIT=1.0
MYSQL_CPU_RESERVATION=0.5

# Redis资源限制
REDIS_MEMORY_LIMIT=512m
REDIS_MEMORY_RESERVATION=256m
REDIS_CPU_LIMIT=0.5
REDIS_CPU_RESERVATION=0.25

# Nacos资源限制
NACOS_MEMORY_LIMIT=1g
NACOS_MEMORY_RESERVATION=512m
NACOS_CPU_LIMIT=1.0
NACOS_CPU_RESERVATION=0.5

# 应用服务资源限制（通用）
APP_MEMORY_LIMIT=512m
APP_MEMORY_RESERVATION=256m
APP_CPU_LIMIT=0.5
APP_CPU_RESERVATION=0.25

# 网关服务资源限制（需要更多资源）
GATEWAY_MEMORY_LIMIT=1g
GATEWAY_MEMORY_RESERVATION=512m
GATEWAY_CPU_LIMIT=1.0
GATEWAY_CPU_RESERVATION=0.5

# ===========================================
# JVM配置
# ===========================================
AUTH_JAVA_OPTS=-Xms256m -Xmx512m -Djava.security.egd=file:/dev/./urandom
WX_AUTH_JAVA_OPTS=-Xms256m -Xmx512m -Djava.security.egd=file:/dev/./urandom
SYSTEM_JAVA_OPTS=-Xms256m -Xmx512m -Djava.security.egd=file:/dev/./urandom
FILE_JAVA_OPTS=-Xms256m -Xmx512m -Djava.security.egd=file:/dev/./urandom
WX_JAVA_OPTS=-Xms256m -Xmx512m -Djava.security.egd=file:/dev/./urandom
GATE_JAVA_OPTS=-Xms256m -Xmx512m -Djava.security.egd=file:/dev/./urandom
GATEWAY_JAVA_OPTS=-Xms512m -Xmx1g -Djava.security.egd=file:/dev/./urandom

# ===========================================
# 健康检查配置
# ===========================================
HEALTH_CHECK_INTERVAL=30s
HEALTH_CHECK_TIMEOUT=10s
HEALTH_CHECK_START_PERIOD=60s
HEALTH_CHECK_RETRIES=3

# ===========================================
# 文件存储配置
# ===========================================
FILE_UPLOAD_PATH=/home/<USER>/images

# ===========================================
# 重启策略配置
# ===========================================
RESTART_POLICY=unless-stopped
