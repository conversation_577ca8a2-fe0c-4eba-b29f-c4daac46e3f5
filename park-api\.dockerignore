# ===========================================
# Docker构建忽略文件
# ===========================================

# 版本控制
.git
.gitignore
.gitattributes

# 环境配置文件
.env
.env.local
.env.*.local

# IDE文件
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 日志文件
logs/
*.log
*.log.*

# 临时文件
tmp/
temp/
*.tmp
*.temp

# 构建产物（保留target目录，因为需要jar文件）
# target/
node_modules/
dist/
build/

# 文档和说明
README.md
*.md
docs/

# Docker相关
Dockerfile
docker-compose*.yml
.dockerignore

# 测试文件
test/
tests/
*test*
*.test

# 备份文件
*.bak
*.backup
*.old

# 压缩文件
*.zip
*.tar
*.tar.gz
*.rar
*.7z

# 配置文件模板
*.example
*.sample
*.template

# 开发工具配置
.editorconfig
.eslintrc*
.prettierrc*
.stylelintrc*

# 包管理器
package-lock.json
yarn.lock
pnpm-lock.yaml

# 其他
*.pid
*.seed
*.pid.lock
.nyc_output
coverage/
