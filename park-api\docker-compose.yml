version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0.35
    container_name: park-mysql
    restart: ${RESTART_POLICY:-unless-stopped}
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: ${MYSQL_DATABASE}
      MYSQL_USER: ${MYSQL_USER}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD}
      TZ: ${TZ}
    ports:
      - "${MYSQL_EXTERNAL_PORT}:${MYSQL_PORT}"
    volumes:
      - mysql_data:/var/lib/mysql
      - mysql_logs:/var/log/mysql
    command: --default-authentication-plugin=mysql_native_password
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p${MYSQL_ROOT_PASSWORD}"]
      timeout: 20s
      retries: 10
      interval: 10s
    deploy:
      resources:
        limits:
          memory: ${MYSQL_MEMORY_LIMIT}
          cpus: '${MYSQL_CPU_LIMIT}'
        reservations:
          memory: ${MYSQL_MEMORY_RESERVATION}
          cpus: '${MYSQL_CPU_RESERVATION}'
    networks:
      - park-network

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: park-redis
    restart: ${RESTART_POLICY:-unless-stopped}
    environment:
      TZ: ${TZ}
    ports:
      - "${REDIS_EXTERNAL_PORT}:${REDIS_PORT}"
    volumes:
      - redis_data:/data
      - redis_logs:/var/log/redis
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "${REDIS_PASSWORD}", "ping"]
      timeout: 10s
      retries: 5
      interval: 10s
    deploy:
      resources:
        limits:
          memory: ${REDIS_MEMORY_LIMIT}
          cpus: '${REDIS_CPU_LIMIT}'
        reservations:
          memory: ${REDIS_MEMORY_RESERVATION}
          cpus: '${REDIS_CPU_RESERVATION}'
    networks:
      - park-network

  # Nacos注册中心
  nacos:
    image: nacos/nacos-server:v2.2.3
    container_name: park-nacos
    restart: ${RESTART_POLICY:-unless-stopped}
    environment:
      MODE: standalone
      SPRING_DATASOURCE_PLATFORM: mysql
      MYSQL_SERVICE_HOST: ${MYSQL_HOST}
      MYSQL_SERVICE_PORT: ${MYSQL_PORT}
      MYSQL_SERVICE_DB_NAME: ${NACOS_DB_NAME}
      MYSQL_SERVICE_USER: ${NACOS_DB_USER}
      MYSQL_SERVICE_PASSWORD: ${NACOS_DB_PASSWORD}
      NACOS_AUTH_ENABLE: ${NACOS_AUTH_ENABLE}
      NACOS_AUTH_TOKEN: ${NACOS_AUTH_TOKEN}
      NACOS_AUTH_IDENTITY_KEY: ${NACOS_AUTH_IDENTITY_KEY}
      NACOS_AUTH_IDENTITY_VALUE: ${NACOS_AUTH_IDENTITY_VALUE}
      TZ: ${TZ}
      # 初始化默认用户
      # NACOS_AUTH_SYSTEM_TYPE: "nacos"
      # NACOS_AUTH_CACHE_ENABLE: "false"
    ports:
      - "${NACOS_HTTP_PORT}:8848"
      - "${NACOS_GRPC_PORT}:9848"
    volumes:
      - nacos_logs:/home/<USER>/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8848/nacos/v1/console/health/readiness"]
      timeout: 10s
      retries: 10
      interval: 15s
    deploy:
      resources:
        limits:
          memory: ${NACOS_MEMORY_LIMIT}
          cpus: '${NACOS_CPU_LIMIT}'
        reservations:
          memory: ${NACOS_MEMORY_RESERVATION}
          cpus: '${NACOS_CPU_RESERVATION}'
    networks:
      - park-network

  # 认证服务
  park-auth:
    image: park/lgjy-auth:3.6.9
    container_name: park-auth
    restart: ${RESTART_POLICY:-unless-stopped}
    ports:
      - "${AUTH_SERVICE_PORT}:9204"
    environment:
      # 环境标识 - 决定加载哪个Nacos配置环境
      SPRING_PROFILES_ACTIVE: ${SPRING_PROFILES_ACTIVE}
      # Nacos连接配置 - 必需的基础连接信息
      SPRING_CLOUD_NACOS_DISCOVERY_SERVER_ADDR: ${NACOS_SERVER_ADDR}
      SPRING_CLOUD_NACOS_CONFIG_SERVER_ADDR: ${NACOS_SERVER_ADDR}
      SPRING_CLOUD_NACOS_USERNAME: ${NACOS_USERNAME}
      SPRING_CLOUD_NACOS_PASSWORD: ${NACOS_PASSWORD}
      SPRING_CLOUD_NACOS_DISCOVERY_USERNAME: ${NACOS_USERNAME}
      SPRING_CLOUD_NACOS_DISCOVERY_PASSWORD: ${NACOS_PASSWORD}
      SPRING_CLOUD_NACOS_DISCOVERY_NAMESPACE: ${NACOS_NAMESPACE}
      SPRING_CLOUD_NACOS_CONFIG_USERNAME: ${NACOS_USERNAME}
      SPRING_CLOUD_NACOS_CONFIG_PASSWORD: ${NACOS_PASSWORD}
      SPRING_CLOUD_NACOS_CONFIG_NAMESPACE: ${NACOS_NAMESPACE}
      TZ: ${TZ}
      JAVA_OPTS: ${AUTH_JAVA_OPTS}
      # 注意：数据库和Redis配置已迁移到Nacos配置中心，支持动态刷新
    volumes:
      - park_auth_logs:/app/logs
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:9204/actuator/health"]
      timeout: ${HEALTH_CHECK_TIMEOUT}
      retries: ${HEALTH_CHECK_RETRIES}
      interval: ${HEALTH_CHECK_INTERVAL}
      start_period: ${HEALTH_CHECK_START_PERIOD}
    deploy:
      resources:
        limits:
          memory: ${APP_MEMORY_LIMIT}
          cpus: '${APP_CPU_LIMIT}'
        reservations:
          memory: ${APP_MEMORY_RESERVATION}
          cpus: '${APP_CPU_RESERVATION}'
    networks:
      - park-network

  # 微信认证服务
  park-wx-auth:
    image: park/lgjy-wx-auth:3.6.9
    container_name: park-wx-auth
    restart: ${RESTART_POLICY:-unless-stopped}
    ports:
      - "${WX_AUTH_SERVICE_PORT}:9205"
    environment:
      # 环境标识 - 决定加载哪个Nacos配置环境
      SPRING_PROFILES_ACTIVE: ${SPRING_PROFILES_ACTIVE}
      # Nacos连接配置 - 必需的基础连接信息
      SPRING_CLOUD_NACOS_DISCOVERY_SERVER_ADDR: ${NACOS_SERVER_ADDR}
      SPRING_CLOUD_NACOS_CONFIG_SERVER_ADDR: ${NACOS_SERVER_ADDR}
      SPRING_CLOUD_NACOS_USERNAME: ${NACOS_USERNAME}
      SPRING_CLOUD_NACOS_PASSWORD: ${NACOS_PASSWORD}
      SPRING_CLOUD_NACOS_DISCOVERY_USERNAME: ${NACOS_USERNAME}
      SPRING_CLOUD_NACOS_DISCOVERY_PASSWORD: ${NACOS_PASSWORD}
      SPRING_CLOUD_NACOS_DISCOVERY_NAMESPACE: ${NACOS_NAMESPACE}
      SPRING_CLOUD_NACOS_CONFIG_USERNAME: ${NACOS_USERNAME}
      SPRING_CLOUD_NACOS_CONFIG_PASSWORD: ${NACOS_PASSWORD}
      SPRING_CLOUD_NACOS_CONFIG_NAMESPACE: ${NACOS_NAMESPACE}
      TZ: ${TZ}
      JAVA_OPTS: ${WX_AUTH_JAVA_OPTS}
      # 注意：Redis、短信服务和微信API配置已迁移到Nacos配置中心，支持动态刷新
    volumes:
      - park_wx_auth_logs:/app/logs
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:9205/actuator/health"]
      timeout: ${HEALTH_CHECK_TIMEOUT}
      retries: ${HEALTH_CHECK_RETRIES}
      interval: ${HEALTH_CHECK_INTERVAL}
      start_period: ${HEALTH_CHECK_START_PERIOD}
    deploy:
      resources:
        limits:
          memory: ${APP_MEMORY_LIMIT}
          cpus: '${APP_CPU_LIMIT}'
        reservations:
          memory: ${APP_MEMORY_RESERVATION}
          cpus: '${APP_CPU_RESERVATION}'
    networks:
      - park-network

  # 系统管理服务
  park-system:
    image: park/lgjy-system:3.6.9
    container_name: park-system
    restart: ${RESTART_POLICY:-unless-stopped}
    ports:
      - "${SYSTEM_SERVICE_PORT}:9201"
    environment:
      # 环境标识 - 决定加载哪个Nacos配置环境
      SPRING_PROFILES_ACTIVE: ${SPRING_PROFILES_ACTIVE}
      # Nacos连接配置 - 必需的基础连接信息
      SPRING_CLOUD_NACOS_DISCOVERY_SERVER_ADDR: ${NACOS_SERVER_ADDR}
      SPRING_CLOUD_NACOS_CONFIG_SERVER_ADDR: ${NACOS_SERVER_ADDR}
      SPRING_CLOUD_NACOS_USERNAME: ${NACOS_USERNAME}
      SPRING_CLOUD_NACOS_PASSWORD: ${NACOS_PASSWORD}
      SPRING_CLOUD_NACOS_DISCOVERY_USERNAME: ${NACOS_USERNAME}
      SPRING_CLOUD_NACOS_DISCOVERY_PASSWORD: ${NACOS_PASSWORD}
      SPRING_CLOUD_NACOS_DISCOVERY_NAMESPACE: ${NACOS_NAMESPACE}
      SPRING_CLOUD_NACOS_CONFIG_USERNAME: ${NACOS_USERNAME}
      SPRING_CLOUD_NACOS_CONFIG_PASSWORD: ${NACOS_PASSWORD}
      SPRING_CLOUD_NACOS_CONFIG_NAMESPACE: ${NACOS_NAMESPACE}
      TZ: ${TZ}
      JAVA_OPTS: ${SYSTEM_JAVA_OPTS}
      # 注意：数据库、Redis和MyBatis配置已迁移到Nacos配置中心，支持动态刷新
    depends_on:
      - mysql
      - redis
      - nacos
    volumes:
      - park_system_logs:/app/logs
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:9201/actuator/health"]
      timeout: ${HEALTH_CHECK_TIMEOUT}
      retries: ${HEALTH_CHECK_RETRIES}
      interval: ${HEALTH_CHECK_INTERVAL}
      start_period: ${HEALTH_CHECK_START_PERIOD}
    deploy:
      resources:
        limits:
          memory: ${APP_MEMORY_LIMIT}
          cpus: '${APP_CPU_LIMIT}'
        reservations:
          memory: ${APP_MEMORY_RESERVATION}
          cpus: '${APP_CPU_RESERVATION}'
    networks:
      - park-network

  # 文件服务
  park-file:
    image: park/lgjy-file:3.6.9
    container_name: park-file
    restart: ${RESTART_POLICY:-unless-stopped}
    ports:
      - "${FILE_SERVICE_PORT}:9202"
    environment:
      # 环境标识 - 决定加载哪个Nacos配置环境
      SPRING_PROFILES_ACTIVE: ${SPRING_PROFILES_ACTIVE}
      # Nacos连接配置 - 必需的基础连接信息
      SPRING_CLOUD_NACOS_DISCOVERY_SERVER_ADDR: ${NACOS_SERVER_ADDR}
      SPRING_CLOUD_NACOS_CONFIG_SERVER_ADDR: ${NACOS_SERVER_ADDR}
      SPRING_CLOUD_NACOS_USERNAME: ${NACOS_USERNAME}
      SPRING_CLOUD_NACOS_PASSWORD: ${NACOS_PASSWORD}
      SPRING_CLOUD_NACOS_DISCOVERY_USERNAME: ${NACOS_USERNAME}
      SPRING_CLOUD_NACOS_DISCOVERY_PASSWORD: ${NACOS_PASSWORD}
      SPRING_CLOUD_NACOS_DISCOVERY_NAMESPACE: ${NACOS_NAMESPACE}
      SPRING_CLOUD_NACOS_CONFIG_USERNAME: ${NACOS_USERNAME}
      SPRING_CLOUD_NACOS_CONFIG_PASSWORD: ${NACOS_PASSWORD}
      SPRING_CLOUD_NACOS_CONFIG_NAMESPACE: ${NACOS_NAMESPACE}
      TZ: ${TZ}
      JAVA_OPTS: ${FILE_JAVA_OPTS}
      # 注意：文件存储配置已迁移到Nacos配置中心，支持动态刷新
    volumes:
      - ${FILE_UPLOAD_PATH}:/app/upload  # ✅ 直接映射
      - park_file_logs:/app/logs
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:9202/actuator/health"]
      timeout: ${HEALTH_CHECK_TIMEOUT}
      retries: ${HEALTH_CHECK_RETRIES}
      interval: ${HEALTH_CHECK_INTERVAL}
      start_period: ${HEALTH_CHECK_START_PERIOD}
    deploy:
      resources:
        limits:
          memory: ${APP_MEMORY_LIMIT}
          cpus: '${APP_CPU_LIMIT}'
        reservations:
          memory: ${APP_MEMORY_RESERVATION}
          cpus: '${APP_CPU_RESERVATION}'
    networks:
      - park-network

  # 微信小程序服务
  park-wx:
    image: park/lgjy-wx:3.6.9
    container_name: park-wx
    restart: ${RESTART_POLICY:-unless-stopped}
    ports:
      - "${WX_SERVICE_PORT}:9206"  # 修复端口冲突：从9202改为9206
    environment:
      # 环境标识 - 决定加载哪个Nacos配置环境
      SPRING_PROFILES_ACTIVE: ${SPRING_PROFILES_ACTIVE}
      # Nacos连接配置 - 必需的基础连接信息
      SPRING_CLOUD_NACOS_DISCOVERY_SERVER_ADDR: ${NACOS_SERVER_ADDR}
      SPRING_CLOUD_NACOS_CONFIG_SERVER_ADDR: ${NACOS_SERVER_ADDR}
      SPRING_CLOUD_NACOS_USERNAME: ${NACOS_USERNAME}
      SPRING_CLOUD_NACOS_PASSWORD: ${NACOS_PASSWORD}
      SPRING_CLOUD_NACOS_DISCOVERY_USERNAME: ${NACOS_USERNAME}
      SPRING_CLOUD_NACOS_DISCOVERY_PASSWORD: ${NACOS_PASSWORD}
      SPRING_CLOUD_NACOS_DISCOVERY_NAMESPACE: ${NACOS_NAMESPACE}
      SPRING_CLOUD_NACOS_CONFIG_USERNAME: ${NACOS_USERNAME}
      SPRING_CLOUD_NACOS_CONFIG_PASSWORD: ${NACOS_PASSWORD}
      SPRING_CLOUD_NACOS_CONFIG_NAMESPACE: ${NACOS_NAMESPACE}
      TZ: ${TZ}
      JAVA_OPTS: ${WX_JAVA_OPTS}
      # 注意：数据库、Redis、微信API和银联支付配置已迁移到Nacos配置中心，支持动态刷新
    depends_on:
      - mysql
      - redis
      - nacos
    volumes:
      - park_wx_logs:/app/logs
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:9206/actuator/health"]
      timeout: ${HEALTH_CHECK_TIMEOUT}
      retries: ${HEALTH_CHECK_RETRIES}
      interval: ${HEALTH_CHECK_INTERVAL}
      start_period: ${HEALTH_CHECK_START_PERIOD}
    deploy:
      resources:
        limits:
          memory: ${APP_MEMORY_LIMIT}
          cpus: '${APP_CPU_LIMIT}'
        reservations:
          memory: ${APP_MEMORY_RESERVATION}
          cpus: '${APP_CPU_RESERVATION}'
    networks:
      - park-network

  # 道闸设备服务
  park-gate:
    image: park/lgjy-gate:3.6.9
    container_name: park-gate
    restart: ${RESTART_POLICY:-unless-stopped}
    ports:
      - "${GATE_SERVICE_PORT}:9203"
    environment:
      # 环境标识 - 决定加载哪个Nacos配置环境
      SPRING_PROFILES_ACTIVE: ${SPRING_PROFILES_ACTIVE}
      # Nacos连接配置 - 必需的基础连接信息
      SPRING_CLOUD_NACOS_DISCOVERY_SERVER_ADDR: ${NACOS_SERVER_ADDR}
      SPRING_CLOUD_NACOS_CONFIG_SERVER_ADDR: ${NACOS_SERVER_ADDR}
      SPRING_CLOUD_NACOS_USERNAME: ${NACOS_USERNAME}
      SPRING_CLOUD_NACOS_PASSWORD: ${NACOS_PASSWORD}
      SPRING_CLOUD_NACOS_DISCOVERY_USERNAME: ${NACOS_USERNAME}
      SPRING_CLOUD_NACOS_DISCOVERY_PASSWORD: ${NACOS_PASSWORD}
      SPRING_CLOUD_NACOS_DISCOVERY_NAMESPACE: ${NACOS_NAMESPACE}
      SPRING_CLOUD_NACOS_CONFIG_USERNAME: ${NACOS_USERNAME}
      SPRING_CLOUD_NACOS_CONFIG_PASSWORD: ${NACOS_PASSWORD}
      SPRING_CLOUD_NACOS_CONFIG_NAMESPACE: ${NACOS_NAMESPACE}
      # 数据库和Redis配置通过环境变量传递给Nacos配置
      DB_HOST: ${MYSQL_HOST}
      DB_PORT: ${MYSQL_PORT}
      DB_NAME: ${MYSQL_DATABASE}
      DB_USERNAME: ${MYSQL_USER}
      DB_PASSWORD: ${MYSQL_PASSWORD}
      REDIS_HOST: ${REDIS_HOST}
      REDIS_PORT: ${REDIS_PORT}
      REDIS_PASSWORD: ${REDIS_PASSWORD}
      REDIS_DATABASE: ${REDIS_DATABASE_GATE}
      TZ: ${TZ}
      JAVA_OPTS: ${GATE_JAVA_OPTS}
      # 注意：数据库和Redis配置已迁移到Nacos配置中心，支持动态刷新
    depends_on:
      - mysql
      - redis
      - nacos
    volumes:
      - park_gate_logs:/app/logs
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:9203/actuator/health"]
      timeout: ${HEALTH_CHECK_TIMEOUT}
      retries: ${HEALTH_CHECK_RETRIES}
      interval: ${HEALTH_CHECK_INTERVAL}
      start_period: ${HEALTH_CHECK_START_PERIOD}
    deploy:
      resources:
        limits:
          memory: ${APP_MEMORY_LIMIT}
          cpus: '${APP_CPU_LIMIT}'
        reservations:
          memory: ${APP_MEMORY_RESERVATION}
          cpus: '${APP_CPU_RESERVATION}'
    networks:
      - park-network

  # 网关服务
  park-gateway:
    image: park/lgjy-gateway:3.6.9
    container_name: park-gateway
    restart: ${RESTART_POLICY:-unless-stopped}
    ports:
      - "${GATEWAY_SERVICE_PORT}:8080"
    environment:
      # 环境标识 - 决定加载哪个Nacos配置环境
      SPRING_PROFILES_ACTIVE: ${SPRING_PROFILES_ACTIVE}
      # Nacos连接配置 - 必需的基础连接信息
      SPRING_CLOUD_NACOS_DISCOVERY_SERVER_ADDR: ${NACOS_SERVER_ADDR}
      SPRING_CLOUD_NACOS_CONFIG_SERVER_ADDR: ${NACOS_SERVER_ADDR}
      SPRING_CLOUD_NACOS_USERNAME: ${NACOS_USERNAME}
      SPRING_CLOUD_NACOS_PASSWORD: ${NACOS_PASSWORD}
      SPRING_CLOUD_NACOS_DISCOVERY_USERNAME: ${NACOS_USERNAME}
      SPRING_CLOUD_NACOS_DISCOVERY_PASSWORD: ${NACOS_PASSWORD}
      SPRING_CLOUD_NACOS_DISCOVERY_NAMESPACE: ${NACOS_NAMESPACE}
      SPRING_CLOUD_NACOS_CONFIG_USERNAME: ${NACOS_USERNAME}
      SPRING_CLOUD_NACOS_CONFIG_PASSWORD: ${NACOS_PASSWORD}
      SPRING_CLOUD_NACOS_CONFIG_NAMESPACE: ${NACOS_NAMESPACE}
      TZ: ${TZ}
      JAVA_OPTS: ${GATEWAY_JAVA_OPTS}
      # 注意：Redis配置已迁移到Nacos配置中心，支持动态刷新
    depends_on:
      - mysql
      - redis
      - nacos
    volumes:
      - park_gateway_logs:/app/logs
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:8080/actuator/health"]
      timeout: ${HEALTH_CHECK_TIMEOUT}
      retries: ${HEALTH_CHECK_RETRIES}
      interval: ${HEALTH_CHECK_INTERVAL}
      start_period: ${HEALTH_CHECK_START_PERIOD}
    deploy:
      resources:
        limits:
          memory: ${GATEWAY_MEMORY_LIMIT}
          cpus: '${GATEWAY_CPU_LIMIT}'
        reservations:
          memory: ${GATEWAY_MEMORY_RESERVATION}
          cpus: '${GATEWAY_CPU_RESERVATION}'
    networks:
      - park-network


volumes:
  mysql_data:
  mysql_logs:
  redis_data:
  redis_logs:
  nacos_logs:
  file_data:
  park_auth_logs:
  park_wx_auth_logs:
  park_system_logs:
  park_file_logs:
  park_wx_logs:
  park_gate_logs:
  park_gateway_logs:

networks:
  park-network:
    driver: bridge